#!/usr/bin/env python3
"""
LaTeX文档语法验证脚本
检查LaTeX文档的基本语法正确性
"""

import re
import sys

def validate_latex_syntax(filename):
    """验证LaTeX文档的基本语法"""
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    errors = []
    warnings = []
    
    # 检查基本结构
    if not re.search(r'\\documentclass', content):
        errors.append("缺少 \\documentclass 声明")
    
    if not re.search(r'\\begin{document}', content):
        errors.append("缺少 \\begin{document}")
    
    if not re.search(r'\\end{document}', content):
        errors.append("缺少 \\end{document}")
    
    # 检查环境匹配
    begin_envs = re.findall(r'\\begin{([^}]+)}', content)
    end_envs = re.findall(r'\\end{([^}]+)}', content)
    
    for env in begin_envs:
        if env not in end_envs:
            errors.append(f"环境 {env} 没有对应的 \\end{{{env}}}")
    
    for env in end_envs:
        if env not in begin_envs:
            errors.append(f"环境 {env} 没有对应的 \\begin{{{env}}}")
    
    # 检查括号匹配
    open_braces = content.count('{')
    close_braces = content.count('}')
    if open_braces != close_braces:
        errors.append(f"大括号不匹配: {open_braces} 个 '{{' vs {close_braces} 个 '}}'")
    
    # 检查数学环境
    equations = re.findall(r'\\begin{equation}.*?\\end{equation}', content, re.DOTALL)
    for i, eq in enumerate(equations):
        if not re.search(r'\\label{', eq):
            warnings.append(f"第 {i+1} 个方程没有标签")
    
    # 检查常见错误
    if re.search(r'\\section{[^}]*\\', content):
        warnings.append("章节标题中可能包含LaTeX命令")
    
    if re.search(r'\$\$', content):
        warnings.append("建议使用 \\[ \\] 而不是 $$")
    
    # 检查中文支持
    if re.search(r'[\u4e00-\u9fff]', content):
        if not re.search(r'\\usepackage.*{ctex}', content):
            warnings.append("文档包含中文但没有加载ctex包")
    
    return errors, warnings

def main():
    if len(sys.argv) != 2:
        print("用法: python validate_latex.py <latex_file>")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    try:
        errors, warnings = validate_latex_syntax(filename)
        
        print(f"验证文件: {filename}")
        print("=" * 50)
        
        if not errors and not warnings:
            print("✅ LaTeX文档语法检查通过！")
        else:
            if errors:
                print("❌ 发现错误:")
                for error in errors:
                    print(f"  - {error}")
            
            if warnings:
                print("⚠️  警告:")
                for warning in warnings:
                    print(f"  - {warning}")
        
        print("\n文档统计:")
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = len(content.split('\n'))
            sections = len(re.findall(r'\\section{', content))
            subsections = len(re.findall(r'\\subsection{', content))
            equations = len(re.findall(r'\\begin{equation}', content))
            
            print(f"  总行数: {lines}")
            print(f"  章节数: {sections}")
            print(f"  小节数: {subsections}")
            print(f"  公式数: {equations}")
    
    except FileNotFoundError:
        print(f"❌ 文件 {filename} 不存在")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
